import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { toggleMenu } from '../store/accessibilitySlice';

export default function Header() {
  const dispatch = useAppDispatch();
  const { darkMode, contrast, biggerText, dyslexia } = useAppSelector(
    (state) => state.accessibility
  );

  const getTextColor = () => {
    if (darkMode) {
      return contrast ? '#ffffff' : '#ffffff';
    }
    return contrast ? '#000000' : '#1f2937';
  };

  const getButtonStyle = () => {
    return {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderColor: 'rgba(255, 255, 255, 0.3)',
    };
  };

  const getFontFamily = () => {
    return dyslexia ? 'System' : 'System';
  };

  return (
    <SafeAreaView edges={['top']}>
      <View className="flex-row items-center justify-between px-4 py-2">
        {/* Status Bar Info */}
        <View className="flex-row items-center">
          <Text 
            className="text-lg font-semibold"
            style={{
              color: getTextColor(),
              fontFamily: getFontFamily(),
              fontSize: biggerText ? 20 : 18,
            }}
          >
            9:41
          </Text>
        </View>

        {/* Right Side - Language and Accessibility */}
        <View className="flex-row items-center space-x-3">
          {/* Language Selector */}
          <TouchableOpacity 
            className="flex-row items-center px-3 py-2 rounded-full border"
            style={getButtonStyle()}
            accessibilityLabel="Language selector"
            accessibilityHint="Currently set to English, tap to change language"
          >
            <View className="w-5 h-3 mr-2">
              <Text>🇿🇦</Text>
            </View>
            <Text 
              className="font-medium mr-1"
              style={{
                color: getTextColor(),
                fontFamily: getFontFamily(),
                fontSize: biggerText ? 16 : 14,
              }}
            >
              English
            </Text>
            <Text 
              style={{
                color: getTextColor(),
                fontSize: biggerText ? 14 : 12,
              }}
            >
              ▼
            </Text>
          </TouchableOpacity>

          {/* Accessibility Button */}
          <TouchableOpacity
            onPress={() => dispatch(toggleMenu())}
            className="flex-row items-center px-4 py-2 rounded-full border"
            style={getButtonStyle()}
            accessibilityLabel="Accessibility menu"
            accessibilityHint="Tap to open accessibility options"
          >
            <View className="w-6 h-6 rounded-full bg-white items-center justify-center mr-2">
              <Text className="text-black font-bold">♿</Text>
            </View>
            <Text 
              className="font-medium"
              style={{
                color: getTextColor(),
                fontFamily: getFontFamily(),
                fontSize: biggerText ? 16 : 14,
              }}
            >
              Accessibility
            </Text>
          </TouchableOpacity>
        </V
