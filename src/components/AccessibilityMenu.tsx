import { View, Text, TouchableOpacity, Modal } from 'react-native';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  toggleDarkMode,
  increaseLineHeight,
  increaseLetterSpacing,
  toggleTextAlign,
  toggleMenu,
} from '../store/accessibilitySlice';

export default function AccessibilityMenu() {
  const dispatch = useAppDispatch();
  const { isMenuOpen, darkMode } = useAppSelector((state) => state.accessibility);

  return (
    <Modal
      transparent={true}
      visible={isMenuOpen}
      animationType="slide"
      onRequestClose={() => dispatch(toggleMenu())}>
      <View className="flex-1 justify-end bg-black bg-opacity-50">
        <View className={`rounded-t-3xl p-6 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <Text className={`mb-4 text-xl font-bold ${darkMode ? 'text-white' : 'text-black'}`}>
            Accessibility Menu
          </Text>

          <MenuItem
            title="Dark/Light Mode"
            onPress={() => dispatch(toggleDarkMode())}
            darkMode={darkMode}
          />
          <MenuItem
            title="Increase Line Height"
            onPress={() => dispatch(increaseLineHeight())}
            darkMode={darkMode}
          />
          <MenuItem
            title="Increase Letter Spacing"
            onPress={() => dispatch(increaseLetterSpacing())}
            darkMode={darkMode}
          />
          <MenuItem
            title="Text Align"
            onPress={() => dispatch(toggleTextAlign())}
            darkMode={darkMode}
          />

          <TouchableOpacity
            onPress={() => dispatch(toggleMenu())}
            className={`mt-4 rounded-lg p-3 ${darkMode ? 'bg-blue-600' : 'bg-blue-500'}`}>
            <Text className="text-center text-white">Close</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
}

interface MenuItemProps {
  title: string;
  onPress: () => void;
  darkMode: boolean;
}

function MenuItem({ title, onPress, darkMode }: MenuItemProps) {
  return (
    <TouchableOpacity
      onPress={onPress}
      className={`mb-2 rounded-lg p-3 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
      <Text className={darkMode ? 'text-white' : 'text-black'}>{title}</Text>
    </TouchableOpacity>
  );
}
